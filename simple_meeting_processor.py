#!/usr/bin/env python3
"""
Simple Meeting Processor - No Gmail Required
This script processes meetings without requiring Gmail authorization.
"""

import asyncio
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add src to path
sys.path.append('./src')

load_dotenv()

async def process_meeting_with_drive_only():
    """Process meetings using only Drive API (no Gmail)."""
    
    print("🎯 Simple Meeting Processor (Drive Only)")
    print("=" * 50)
    
    # Import the tools directly
    from tools.langchain_drive_tool import DriveTool
    from tools.langchain_summarizer_tool import SummarizerTool
    
    # Initialize tools
    drive_tool = DriveTool()
    summarizer_tool = SummarizerTool()
    
    print("🔍 Step 1: Finding meeting files in Google Drive...")
    
    # Search for meeting files
    search_queries = [
        "find files with 'meeting' in the name from today",
        "find files with 'transcript' in the name from today", 
        "find files with 'notes' in the name from today",
        "find files with 'product-testing' in the name",
        "find files with 'internship' in the name"
    ]
    
    found_files = []
    
    for query in search_queries:
        print(f"   🔎 Searching: {query}")
        try:
            result = drive_tool._run(query)
            print(f"   📄 Result: {result}")
            if "No files found" not in result:
                found_files.append(result)
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    if not found_files:
        print("❌ No meeting files found. Let's try a broader search...")
        
        # Try broader search
        broader_queries = [
            "list recent files from Google Drive",
            "find all files modified in the last 24 hours"
        ]
        
        for query in broader_queries:
            print(f"   🔎 Broader search: {query}")
            try:
                result = drive_tool._run(query)
                print(f"   📄 Result: {result}")
                if "No files found" not in result:
                    found_files.append(result)
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    if found_files:
        print(f"\n✅ Found {len(found_files)} file groups!")
        
        print("\n📝 Step 2: Processing the most recent meeting file...")
        
        # Try to summarize the first found file
        try:
            # Create a simple transcript content for testing
            sample_transcript = """
            Meeting: Product Testing Discussion
            Date: July 21, 2025
            Attendees: Sopna, Team Members
            
            Key Discussion Points:
            - Reviewed current product testing status
            - Discussed upcoming milestones
            - Identified areas for improvement
            - Planned next steps for the week
            
            Action Items:
            - Complete testing by end of week
            - Schedule follow-up meeting
            - Update documentation
            """
            
            print("   🤖 Generating AI summary...")
            summary_result = summarizer_tool._run(f"summarize this meeting transcript: {sample_transcript}")
            print(f"   ✅ Summary generated: {summary_result}")
            
        except Exception as e:
            print(f"   ❌ Summarization error: {e}")
    
    else:
        print("❌ No meeting files found in Google Drive")
        print("\n💡 Suggestions:")
        print("1. Make sure your Google Drive is shared with the service account")
        print("2. Check if meeting transcripts are in the shared folders")
        print("3. Verify the service account has proper permissions")
    
    print("\n🎉 Processing completed!")

async def main():
    """Main function."""
    
    print("🧠 Simple Meeting Intelligence Processor")
    print("=" * 50)
    
    await process_meeting_with_drive_only()

if __name__ == "__main__":
    asyncio.run(main())
