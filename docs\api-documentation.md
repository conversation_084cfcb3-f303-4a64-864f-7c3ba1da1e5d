# 📡 API Documentation

## Overview

The Meeting Intelligence Agent provides a comprehensive REST API built with FastAPI, featuring LangChain agent integration for intelligent meeting processing and calendar attachment functionality.

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, the API uses Google OAuth 2.0 for Google Workspace integration. Ensure your service account credentials are properly configured.

## Core Endpoints

### Health Check

**GET** `/health`

Returns system health status and configuration.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-18T12:00:00Z",
  "version": "1.0.0",
  "services": {
    "database": "connected",
    "google_auth": "authenticated",
    "langchain_agent": "initialized"
  }
}
```

### API Information

**GET** `/`

Returns API capabilities and available endpoints.

**Response:**
```json
{
  "name": "Meeting Intelligence Agent API",
  "version": "1.0.0",
  "description": "LangChain-powered meeting intelligence with 6-step workflow",
  "features": [
    "AI-powered meeting summarization",
    "Email notifications",
    "Calendar integration",
    "Google Drive storage",
    "Interactive chat interface"
  ]
}
```

## Workflow Endpoints

### Trigger Complete Workflow

**POST** `/agent/trigger-workflow`

Executes the complete 6-step Post meetingworkflow.

**Request Body:** None required

**Response:**
```json
{
  "status": "success",
  "workflow_id": "wf_20250718_120000",
  "steps_completed": 6,
  "meetings_processed": 1,
  "summaries_generated": 1,
  "emails_sent": 2,
  "calendar_attachments": 1,
  "execution_time": "45.2s"
}
```

### Workflow Status

**GET** `/agent/workflow-status`

Returns current workflow capabilities and status.

**Response:**
```json
{
  "agent_status": "ready",
  "tools_available": 6,
  "tools": [
    "calendar_tool",
    "drive_tool", 
    "summarizer_tool",
    "notification_tool",
    "calendar_attachment_tool",
    "file_manager_tool"
  ],
  "last_execution": "2025-07-18T11:30:00Z",
  "scheduler_active": true
}
```

### Interactive Chat

**POST** `/agent/chat`

Communicate directly with the LangChain agent for custom operations.

**Request Body:**
```json
{
  "message": "attach file meeting_summaries/summary.html to event 'product-testing' from today"
}
```

**Response:**
```json
{
  "response": "Successfully attached meeting summary to calendar event 'product-testing'",
  "agent_actions": [
    "Found calendar event: product-testing",
    "Uploaded file to Google Drive",
    "Attached file to calendar event"
  ],
  "execution_time": "12.3s"
}
```

## Scheduler Endpoints

### Start Scheduler

**POST** `/agent/start-scheduler`

Starts automated workflow execution every 30 minutes.

**Response:**
```json
{
  "status": "scheduler_started",
  "interval": "30 minutes",
  "next_execution": "2025-07-18T12:30:00Z"
}
```

### Stop Scheduler

**POST** `/agent/stop-scheduler`

Stops automated workflow execution.

**Response:**
```json
{
  "status": "scheduler_stopped",
  "last_execution": "2025-07-18T12:00:00Z"
}
```

### Scheduler Status

**GET** `/agent/scheduler-status`

Returns current scheduler status.

**Response:**
```json
{
  "active": true,
  "interval": "30 minutes",
  "last_execution": "2025-07-18T12:00:00Z",
  "next_execution": "2025-07-18T12:30:00Z",
  "total_executions": 24
}
```

## Chat Interface Commands

The `/agent/chat` endpoint supports various natural language commands:

### Workflow Commands

- `"Execute the Post meetingworkflow"`
- `"Run the complete 6-step workflow"`
- `"Process meetings from the last 30 minutes"`

### Meeting Operations

- `"Find meetings from today"`
- `"Search for transcript files"`
- `"Read transcript with ID 1234567890"`

### Summarization

- `"Summarize the product-testing meeting"`
- `"Generate HTML summary for meeting transcript"`
- `"Create JSON summary with action items"`

### Email Notifications

- `"Send meeting <NAME_EMAIL>"`
- `"Email notification to attendees with subject: Meeting Summary"`
- `"Notify participants about the meeting results"`

### Calendar Integration

- `"Attach file summary.html to event 'product-testing' from today"`
- `"Link meeting summary to calendar event"`
- `"Upload summary to Google Drive and attach to calendar"`

### File Operations

- `"Upload file to Google Drive"`
- `"Save summary to meeting_summaries folder"`
- `"Organize files in Drive structure"`

## Error Responses

### Standard Error Format

```json
{
  "error": "error_code",
  "message": "Human readable error message",
  "details": {
    "step": "workflow_step_name",
    "tool": "tool_name",
    "timestamp": "2025-07-18T12:00:00Z"
  }
}
```

### Common Error Codes

- `authentication_failed`: Google OAuth authentication issues
- `workflow_execution_failed`: LangChain agent execution error
- `tool_unavailable`: Required tool not accessible
- `calendar_event_not_found`: Calendar event doesn't exist
- `file_upload_failed`: Google Drive upload error
- `email_send_failed`: Email notification error

## Rate Limits

- **Workflow Execution**: 1 request per minute
- **Chat Interface**: 10 requests per minute
- **Status Endpoints**: 60 requests per minute

## Interactive API Documentation

Visit `http://localhost:8000/docs` for interactive Swagger UI documentation with:

- Live API testing
- Request/response examples
- Schema definitions
- Authentication setup

## WebSocket Support (Future)

Planned WebSocket endpoint for real-time workflow updates:

```
ws://localhost:8000/ws/workflow-updates
```

## SDK Examples

### Python

```python
import requests

# Trigger workflow
response = requests.post("http://localhost:8000/agent/trigger-workflow")
print(response.json())

# Chat with agent
chat_response = requests.post(
    "http://localhost:8000/agent/chat",
    json={"message": "Execute the Post meetingworkflow"}
)
print(chat_response.json())
```

### JavaScript

```javascript
// Trigger workflow
fetch('http://localhost:8000/agent/trigger-workflow', {
  method: 'POST'
})
.then(response => response.json())
.then(data => console.log(data));

// Chat with agent
fetch('http://localhost:8000/agent/chat', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({
    message: "Attach summary to calendar event"
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## Monitoring and Logging

All API requests are logged with:
- Request timestamp
- Endpoint accessed
- Response time
- Success/error status
- Agent actions performed

Logs are available in the `./logging/` directory.
