#!/usr/bin/env python3
"""
Test Google Authentication for Meeting Intelligence Agent
This script tests if Google Calendar and Drive APIs are working properly.
"""

import os
import sys
from datetime import datetime, timedelta
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

def test_google_authentication():
    """Test Google Calendar and Drive API authentication."""
    
    print("🔐 Testing Google Authentication...")
    print("=" * 50)
    
    # Get credentials path
    creds_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', './keys/google-service-account.json')
    project_id = os.getenv('GOOGLE_PROJECT_ID', 'elevation-agent-dev')
    
    print(f"📁 Credentials file: {creds_path}")
    print(f"🏗️  Project ID: {project_id}")
    
    # Check if credentials file exists
    if not os.path.exists(creds_path):
        print(f"❌ Credentials file not found: {creds_path}")
        return False
    
    try:
        # Load service account credentials
        credentials = service_account.Credentials.from_service_account_file(
            creds_path,
            scopes=[
                'https://www.googleapis.com/auth/calendar.readonly',
                'https://www.googleapis.com/auth/drive.readonly',
                'https://www.googleapis.com/auth/drive.file'
            ]
        )
        
        print("✅ Service account credentials loaded successfully")
        print(f"📧 Service account email: {credentials.service_account_email}")
        
    except Exception as e:
        print(f"❌ Failed to load credentials: {e}")
        return False
    
    # Test Calendar API
    print("\n📅 Testing Google Calendar API...")
    try:
        calendar_service = build('calendar', 'v3', credentials=credentials)
        
        # Try to get calendar list
        calendar_list = calendar_service.calendarList().list().execute()
        calendars = calendar_list.get('items', [])
        
        print(f"✅ Calendar API working! Found {len(calendars)} calendars")
        
        if calendars:
            print("📋 Available calendars:")
            for calendar in calendars[:3]:  # Show first 3
                print(f"   - {calendar.get('summary', 'No name')} ({calendar.get('id', 'No ID')})")
        
        # Try to get recent events
        now = datetime.utcnow()
        start_time = (now - timedelta(hours=24)).isoformat() + 'Z'
        end_time = now.isoformat() + 'Z'
        
        events_result = calendar_service.events().list(
            calendarId='primary',
            timeMin=start_time,
            timeMax=end_time,
            maxResults=10,
            singleEvents=True,
            orderBy='startTime'
        ).execute()
        
        events = events_result.get('items', [])
        print(f"📊 Found {len(events)} events in the last 24 hours")
        
        if events:
            print("🗓️  Recent events:")
            for event in events[:3]:  # Show first 3
                start = event['start'].get('dateTime', event['start'].get('date'))
                print(f"   - {event.get('summary', 'No title')} at {start}")
        
    except HttpError as e:
        if e.resp.status == 403:
            print("❌ Calendar API access denied. Please check:")
            print("   1. Calendar API is enabled in Google Cloud Console")
            print("   2. Service account has calendar access permissions")
            print("   3. Calendar is shared with service account email")
        else:
            print(f"❌ Calendar API error: {e}")
        return False
    except Exception as e:
        print(f"❌ Calendar API error: {e}")
        return False
    
    # Test Drive API
    print("\n📁 Testing Google Drive API...")
    try:
        drive_service = build('drive', 'v3', credentials=credentials)
        
        # Try to list files
        results = drive_service.files().list(
            pageSize=10,
            fields="nextPageToken, files(id, name, mimeType, modifiedTime)"
        ).execute()
        
        files = results.get('files', [])
        print(f"✅ Drive API working! Found {len(files)} accessible files")
        
        if files:
            print("📄 Recent files:")
            for file in files[:3]:  # Show first 3
                print(f"   - {file.get('name', 'No name')} ({file.get('mimeType', 'Unknown type')})")
        
    except HttpError as e:
        if e.resp.status == 403:
            print("❌ Drive API access denied. Please check:")
            print("   1. Drive API is enabled in Google Cloud Console")
            print("   2. Service account has drive access permissions")
            print("   3. Drive folders are shared with service account email")
        else:
            print(f"❌ Drive API error: {e}")
        return False
    except Exception as e:
        print(f"❌ Drive API error: {e}")
        return False
    
    print("\n🎉 All Google APIs are working correctly!")
    print("\n📋 Next Steps:")
    print("1. Make sure your calendar is shared with the service account")
    print("2. Make sure your drive folders are shared with the service account")
    print("3. Add meeting transcripts to your shared drive folder")
    print("4. Run the meeting intelligence agent")
    
    return True

if __name__ == "__main__":
    success = test_google_authentication()
    sys.exit(0 if success else 1)
