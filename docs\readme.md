# 🤖 Meeting Intelligence Agent

[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104.0-green.svg)](https://fastapi.tiangolo.com)
[![<PERSON><PERSON>hain](https://img.shields.io/badge/LangChain-0.2.0-orange.svg)](https://langchain.com)
[![Google Cloud](https://img.shields.io/badge/Google%20Cloud-Vertex%20AI-red.svg)](https://cloud.google.com/vertex-ai)

A streamlined AI-powered meeting intelligence system that processes meeting transcripts through a comprehensive 6-step workflow. Built with LangChain for intelligent orchestration and Google Cloud Vertex AI for enterprise-grade AI capabilities.

## 🎯 Overview

The Post meeting Intelligence Agent is a production-ready system that executes a comprehensive workflow:

**1. Identify Meeting & Transcript** → **2. Summarize Transcript (AI)** → **3. Generate JSON & HTML Summaries** → **4. <PERSON><PERSON> Summaries to Attendees** → **5. Store Summaries in Google Drive** → **6. Attach Summary to Calendar Event**

### ✨ Key Features

- **🔄 Comprehensive Post meetingWorkflow**: Complete 6-step processing pipeline
- **🧠 LangChain Orchestration**: Intelligent tool selection and workflow management
- **🤖 AI-Powered Summarization**: Google Cloud Vertex AI + Gemini Pro
- **📧 Professional Email Distribution**: HTML-formatted summaries to attendees
- **📁 Organized Drive Storage**: Structured file organization with project categorization
- **📅 Calendar Integration**: Automatic attachment of summaries to calendar events
- **⚡ Scheduled Processing**: Automated 30-minute interval execution
- **🔍 Smart Duplicate Prevention**: Avoid reprocessing the same meetings
- **📊 Real-time Monitoring**: Comprehensive API endpoints for status tracking
- **💬 Interactive Chat Interface**: Direct communication with the LangChain agent

## 🏗 System Architecture

### Technology Stack

- **Framework**: FastAPI 0.104.0
- **AI Orchestration**: LangChain 0.2.0 with ChatVertexAI
- **AI Provider**: Google Cloud Vertex AI (Gemini 2.0 Flash)
- **Database**: MySQL 8.0
- **Authentication**: Google OAuth 2.0
- **Notifications**: Email (Gmail API + SendGrid)
- **Scheduling**: Python Schedule + Cron

### Core Components

```
src/
├── api/                       # FastAPI REST API
│   ├── main.py               # FastAPI application
│   └── routers/              # API endpoints
├── agents/                   # LangChain agent
│   └── langchain_meeting_agent.py    # Post meetingworkflow orchestrator
├── tools/                    # LangChain tools (6 specialized tools)
│   ├── langchain_calendar_tool.py         # Google Calendar access
│   ├── langchain_drive_tool.py            # Google Drive operations
│   ├── langchain_summarizer_tool.py       # AI summarization
│   ├── langchain_notification_tool.py     # Email notifications
│   ├── langchain_calendar_attachment_tool.py # Calendar attachment
│   └── langchain_file_manager_tool.py     # File operations
├── services/                 # Core business services
│   ├── ai_summarizer.py      # LangChain + Vertex AI integration
│   └── email_service.py      # Email delivery service
├── utility/                  # Shared utilities
│   ├── google_auth.py        # Google authentication
│   ├── calendar_service.py   # Calendar operations
│   └── cron_workflow.py      # Automated scheduling
├── configuration/            # Database configuration
│   ├── config.py             # Database settings
│   └── db.py                 # Database connection
└── constants/                # Application constants
    ├── app.py                # Application settings
    ├── errors.py             # Error definitions
    └── tables.py             # Database tables
```

## 🚀 Quick Start

> **⚡ New to the system?** Check out our [Quick Start Guide](quick-start.md) for a 5-minute setup!

### Prerequisites

- Python 3.8+
- Google Cloud Project with Vertex AI enabled
- Google Workspace account for Calendar/Drive access

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd meeting-intelligence-agent

# Create virtual environment (recommended)
python -m venv venvagent
source venvagent/bin/activate  # On Windows: venvagent\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Create necessary directories
mkdir -p keys meeting_summaries logging
```

### 2. Environment Configuration

Create a `.env` file in the project root:

```bash
# Google Cloud Configuration
GOOGLE_PROJECT_ID=your-google-cloud-project-id
GOOGLE_APPLICATION_CREDENTIALS=./keys/google-service-account.json
VERTEX_AI_LOCATION=us-central1

# Gmail API Configuration
GMAIL_CREDENTIALS_PATH=./keys/gmail-credentials.json

# Database Configuration
DB_HOST=your-mysql-host
DB_PORT=3306
DB_NAME=meeting_intelligence
DB_USER=your-db-user
DB_PASSWORD=your-db-password

# Email Provider (gmail or sendgrid)
EMAIL_PROVIDER=gmail
```

### 3. Quick Test

```bash
# Test the workflow manually
python run_agent.py

# Start the API server
python start_api.py --port 8000

# Test the API endpoints
curl http://localhost:8000/health
curl http://localhost:8000/docs  # Interactive API documentation
```

## 🔧 Running the System

### Option 1: API Server (Recommended)

```bash
# Start the FastAPI server
python start_api.py

# Start automated processing (30-minute intervals)
curl -X POST http://localhost:8000/agent/start-scheduler

# Check status
curl http://localhost:8000/agent/workflow-status
```

### Option 2: Manual Execution

```bash
# Run workflow once
python -c "
from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow
import asyncio
result = asyncio.run(run_autonomous_meeting_workflow())
print(result)
"
```

### Option 3: Production Deployment

```bash
# Using cron for automated execution
crontab -e
# Add: */30 * * * * cd /path/to/meeting-intelligence-agent && python -c "from src.agents.langchain_meeting_agent import run_autonomous_meeting_workflow; import asyncio; asyncio.run(run_autonomous_meeting_workflow())"
```

## 📡 API Endpoints

### Core Endpoints

| Endpoint  | Method | Description                      |
| --------- | ------ | -------------------------------- |
| `/`       | GET    | API information and capabilities |
| `/health` | GET    | System health check              |
| `/docs`   | GET    | Interactive API documentation    |

### Workflow Endpoints

| Endpoint                  | Method | Description                                    |
| ------------------------- | ------ | ---------------------------------------------- |
| `/agent/trigger-workflow` | POST   | Execute complete 6-step Post meetingworkflow  |
| `/agent/workflow-status`  | GET    | Get workflow status and capabilities           |
| `/agent/chat`             | POST   | Interactive chat with LangChain agent          |
| `/agent/start-scheduler`  | POST   | Start automated processing (30-min intervals)  |
| `/agent/stop-scheduler`   | POST   | Stop automated processing                      |
| `/agent/scheduler-status` | GET    | Check scheduler status                         |

### Example Usage

```bash
# Get workflow status
curl http://localhost:8000/agent/workflow-status

# Trigger workflow manually
curl -X POST http://localhost:8000/agent/trigger-workflow

# Interactive chat
curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "What meetings have you processed today?"}'

# Start automated processing
curl -X POST http://localhost:8000/agent/start-scheduler

# Interactive chat with LangChain agent
curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Attach the latest meeting summary to the product-testing calendar event"}'
```

## 💬 LangChain Agent Chat Interface

The system includes an interactive chat interface that allows direct communication with the LangChain agent:

### Available Commands

- **Workflow Execution**: `"Execute the Post meetingworkflow"`
- **Meeting Search**: `"Find meetings from the last hour"`
- **Summary Generation**: `"Summarize the product-testing meeting transcript"`
- **Email Notifications**: `"Send meeting summary to attendees"`
- **Calendar Attachment**: `"Attach summary to calendar event"`
- **File Operations**: `"Upload meeting summary to Google Drive"`

### Example Chat Interactions

```bash
# Execute complete workflow
curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Execute the Post meetingworkflow for meetings in the last 30 minutes"}'

# Attach summary to calendar
curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "attach file meeting_summaries/summary.html to event \"product-testing\" from today"}'

# Send notifications
curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Send email <NAME_EMAIL> with subject: Meeting Summary"}'
```

## 🔄 Post meetingWorkflow Details

### Step 1: Identify Meeting & Transcript

- **CalendarTool**: Scans Google Calendar for meetings in the last 30 minutes
- **DriveTool**: Searches Google Drive for matching transcript files
- **Matching Logic**: Time-based (±5 minutes) and title-based matching

### Step 2: Summarize Transcript (AI)

- **SummarizerTool**: Processes transcript content using AI
- **AI Models**: Google Cloud Vertex AI + Gemini Pro
- **Output**: Structured summary with key insights and action items

### Step 3: Generate JSON & HTML Summaries

- **JSON Format**: Structured data for storage and APIs
- **HTML Format**: Professional styling for email distribution
- **Content**: Executive summary, key decisions, action items

### Step 4: Email Summaries to Attendees

- **NotificationTool**: Sends HTML summaries to all meeting attendees
- **Email Service**: Gmail API integration
- **Content**: Professional formatting with meeting details

### Step 5: Store Summaries in Google Drive

- **DriveTool**: Organizes files in structured folders
- **Folder Structure**: `/Meeting_Summaries_HTML/YYYY/MM/Project-Name/`
- **File Types**: Both JSON and HTML versions stored

### Step 6: Attach Summary to Calendar Event

- **CalendarAttachmentTool**: Links HTML summaries to original calendar events
- **Drive Upload**: Uploads summary to "Meeting Summaries" folder in Google Drive
- **Calendar Integration**: Attaches Drive file to the corresponding calendar event
- **Permanent Access**: Creates clickable attachment in calendar for easy access

## 🛠 Configuration

### Environment Variables

```bash
# Required: Google Cloud Configuration
GOOGLE_PROJECT_ID=your-google-cloud-project-id
GOOGLE_APPLICATION_CREDENTIALS=./keys/google-service-account.json
VERTEX_AI_LOCATION=us-central1

# Required: Gmail API
GMAIL_CREDENTIALS_PATH=./keys/gmail-credentials.json
GMAIL_TOKEN_PATH=./keys/gmail-token.json

# Optional: Gemini API (fallback)
GEMINI_API_KEY=your-gemini-api-key
```

### Workflow Configuration

```python
# Default settings (30-minute intervals)
TIME_WINDOW_MINUTES = 30
ENABLE_SCHEDULER = True
CLEANUP_TEMP_FILES = True
```

## 🗂 File Organization

The system automatically organizes meeting summaries:

```
output/
├── html/                      # HTML summaries
├── json/                      # JSON summaries
└── Meeting_Summaries_HTML/    # Google Drive structure
    └── 2025/
        └── 01/
            ├── Project-Alpha/
            │   ├── Daily-Standup-2025-01-13.html
            │   └── Daily-Standup-2025-01-13.json
            └── Project-Beta/
                ├── Sprint-Review-2025-01-13.html
                └── Sprint-Review-2025-01-13.json
```

## 🔧 Troubleshooting

### Common Issues

**Q: "AI libraries not available" error**
A: Install AI dependencies: `pip install google-generativeai google-cloud-aiplatform`

**Q: Authentication errors**
A: Check that service account credentials are in `./keys/google-service-account.json`

**Q: No meetings found**
A: Ensure your calendar is shared with the service account email

**Q: Email sending fails**
A: Verify Gmail API credentials and token files are properly configured

### System Health

```bash
# Check system health
curl http://localhost:8000/health

# Check workflow capabilities
curl http://localhost:8000/agent/workflow-status
```

## 🔒 Security & Permissions

### Google Cloud Setup

1. Create service account with Calendar and Drive permissions
2. Enable Vertex AI in your Google Cloud project
3. Download service account credentials JSON

### Gmail API Setup

1. Create Gmail API credentials in Google Cloud Console
2. Configure OAuth consent screen
3. Download credentials and generate token

### Drive & Calendar Sharing

Share your Google Calendar and Drive folders with:

```
<EMAIL>
```

## 🚀 Production Deployment

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "start_api.py"]
```

### Systemd Service

```ini
[Unit]
Description=Meeting Intelligence Agent
After=network.target

[Service]
Type=simple
User=meetingagent
WorkingDirectory=/path/to/meeting-intelligence-agent
ExecStart=/usr/bin/python start_api.py
Restart=always

[Install]
WantedBy=multi-user.target
```

## 📊 Monitoring

### Health Checks

- `/health` endpoint for system status
- `/agent/workflow-status` for workflow capabilities
- `/agent/scheduler-status` for automation status

### Logging

- Structured logging to `./logging/` directory
- Real-time workflow progress tracking
- Error reporting and debugging information

## 🔮 Future Enhancements

- **Multi-tenant Support**: Handle multiple organizations
- **Advanced Analytics**: Meeting insights and trends
- **Integration APIs**: Connect with other business tools
- **Custom Templates**: Configurable summary formats
- **Batch Processing**: Handle multiple meetings simultaneously

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **LangChain**: For intelligent agent orchestration
- **Google Cloud**: For Vertex AI and Workspace integrations
- **FastAPI**: For high-performance API framework

## 📚 Documentation

### Comprehensive Guides

- **[Quick Start Guide](quick-start.md)** - Get running in 5 minutes! ⚡
- **[API Documentation](api-documentation.md)** - Complete API reference with examples
- **[Deployment Guide](deployment-guide.md)** - Production deployment with Docker, Kubernetes, and systemd
- **[Changelog](../CHANGELOG.md)** - Version history and upgrade instructions

### Quick Links

- **Interactive API Docs**: `http://localhost:8000/docs` (Swagger UI)
- **Health Check**: `http://localhost:8000/health`
- **Workflow Status**: `http://localhost:8000/agent/workflow-status`

## 📞 Support

For support and questions:

- Create an issue in the repository
- Check the troubleshooting section above
- Review the [API documentation](api-documentation.md)
- Check the [deployment guide](deployment-guide.md) for production issues

## 🔄 Version Information

- **Current Version**: 2.0.0
- **LangChain Integration**: ✅ Complete
- **Calendar Attachment**: ✅ Implemented
- **6-Step Workflow**: ✅ Active

See [CHANGELOG.md](../CHANGELOG.md) for detailed version history and upgrade instructions.

---

_Built with ❤️ for intelligent meeting management using LangChain and Google Cloud AI_
