"""Email service for Meeting Intelligence Agent."""

import os
import logging
import base64
from typing import Dict, Any, List, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders

# Google Gmail API
try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
except ImportError:
    Request = None
    Credentials = None
    build = None
    HttpError = None

# SendGrid
try:
    from sendgrid import SendGridAPIClient
    from sendgrid.helpers.mail import Mail, Email, To, Content
except ImportError:
    SendGridAPIClient = None
    Mail = None

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending emails via Gmail API or SendGrid."""
    
    def __init__(self, provider: str = "gmail"):
        """
        Initialize email service.
        
        Args:
            provider: Email provider ("gmail")
        """
        self.provider = provider.lower()
        self.gmail_service = None
        self.sendgrid_client = None
        
        if self.provider == "gmail":
            self._init_gmail()
        else:
            raise ValueError(f"Unsupported email provider: {provider}")
    
    def _init_gmail(self):
        """Initialize Gmail API service."""
        try:
            if not all([Request, Credentials, build]):
                raise ImportError("Google API client libraries not available")
            
            SCOPES = ['https://www.googleapis.com/auth/gmail.send']
            creds = None
            
            # Load existing credentials
            token_path = os.getenv('GMAIL_TOKEN_PATH', './keys/gmail-token.json')
            if os.path.exists(token_path):
                creds = Credentials.from_authorized_user_file(token_path, SCOPES)
            
            # If no valid credentials, get new ones
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    credentials_path = os.getenv('GMAIL_CREDENTIALS_PATH', './keys/gmail-credentials.json')
                    if not os.path.exists(credentials_path):
                        raise FileNotFoundError(f"Gmail credentials file not found: {credentials_path}")
                    
                    flow = InstalledAppFlow.from_client_secrets_file(credentials_path, SCOPES)
                    creds = flow.run_local_server(port=0)
                
                # Save credentials for next run
                with open(token_path, 'w') as token:
                    token.write(creds.to_json())
            
            self.gmail_service = build('gmail', 'v1', credentials=creds)
            logger.info("Gmail API service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gmail API: {e}")
            self.gmail_service = None
    
    def _init_sendgrid(self):
        """Initialize SendGrid service."""
        try:
            if not SendGridAPIClient:
                raise ImportError("SendGrid library not available")
            
            api_key = os.getenv('SENDGRID_API_KEY')
            if not api_key:
                raise ValueError("SENDGRID_API_KEY environment variable not set")
            
            self.sendgrid_client = SendGridAPIClient(api_key=api_key)
            logger.info("SendGrid service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize SendGrid: {e}")
            self.sendgrid_client = None
    
    async def send_email(self, to_email: str, subject: str, body: str,
                        html_body: Optional[str] = None,
                        attachments: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Send email using configured provider.
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            body: Email body (plain text)
            html_body: Email body (HTML)
            attachments: List of file paths to attach
            
        Returns:
            Result dictionary with status and message_id
        """
        try:
            if self.provider == "gmail":
                return await self._send_gmail(to_email, subject, body, html_body, attachments)
            elif self.provider == "sendgrid":
                return await self._send_sendgrid(to_email, subject, body, html_body, attachments)
            else:
                raise ValueError(f"Unsupported provider: {self.provider}")
                
        except Exception as e:
            logger.error(f"Error sending email: {e}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def _send_gmail(self, to_email: str, subject: str, body: str,
                         html_body: Optional[str] = None,
                         attachments: Optional[List[str]] = None) -> Dict[str, Any]:
        """Send email via Gmail API."""
        try:
            if not self.gmail_service:
                raise ValueError("Gmail service not initialized")
            
            from_email = os.getenv('GMAIL_FROM_EMAIL')
            if not from_email:
                raise ValueError("GMAIL_FROM_EMAIL environment variable not set")
            
            # Create message
            message = MIMEMultipart('alternative')
            message['to'] = to_email
            message['from'] = from_email
            message['subject'] = subject
            
            # Add text part
            text_part = MIMEText(body, 'plain')
            message.attach(text_part)
            
            # Add HTML part if provided
            if html_body:
                html_part = MIMEText(html_body, 'html')
                message.attach(html_part)
            
            # Add attachments if provided
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        with open(file_path, 'rb') as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(file_path)}'
                        )
                        message.attach(part)
            
            # Encode message
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
            
            # Send message
            result = self.gmail_service.users().messages().send(
                userId='me',
                body={'raw': raw_message}
            ).execute()
            
            return {
                "status": "success",
                "message_id": result.get('id'),
                "provider": "gmail"
            }
            
        except Exception as e:
            logger.error(f"Gmail send error: {e}")
            raise
    
    async def _send_sendgrid(self, to_email: str, subject: str, body: str,
                            html_body: Optional[str] = None,
                            attachments: Optional[List[str]] = None) -> Dict[str, Any]:
        """Send email via SendGrid."""
        try:
            if not self.sendgrid_client:
                raise ValueError("SendGrid client not initialized")
            
            from_email = os.getenv('SENDGRID_FROM_EMAIL')
            if not from_email:
                raise ValueError("SENDGRID_FROM_EMAIL environment variable not set")
            
            # Create message
            message = Mail(
                from_email=Email(from_email),
                to_emails=To(to_email),
                subject=subject,
                plain_text_content=Content("text/plain", body)
            )
            
            # Add HTML content if provided
            if html_body:
                message.content = [
                    Content("text/plain", body),
                    Content("text/html", html_body)
                ]
            
            # Add attachments if provided
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        with open(file_path, 'rb') as f:
                            data = f.read()
                            encoded = base64.b64encode(data).decode()
                        
                        attachment = {
                            "content": encoded,
                            "type": "application/octet-stream",
                            "filename": os.path.basename(file_path),
                            "disposition": "attachment"
                        }
                        message.attachment = [attachment]
            
            # Send message
            response = self.sendgrid_client.send(message)
            
            return {
                "status": "success",
                "message_id": response.headers.get('X-Message-Id'),
                "provider": "sendgrid",
                "status_code": response.status_code
            }
            
        except Exception as e:
            logger.error(f"SendGrid send error: {e}")
            raise
    
    async def check_health(self) -> bool:
        """Check if email service is healthy."""
        try:
            if self.provider == "gmail":
                return self.gmail_service is not None
            elif self.provider == "sendgrid":
                return self.sendgrid_client is not None
            return False
            
        except Exception as e:
            logger.error(f"Email service health check failed: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Test email service connection."""
        try:
            if self.provider == "gmail":
                return self.gmail_service is not None
            elif self.provider == "sendgrid":
                return self.sendgrid_client is not None
            return False
        except Exception as e:
            logger.error(f"Email connection test failed: {e}")
            return False

    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the email provider."""
        return {
            "provider": self.provider,
            "initialized": (
                self.gmail_service is not None if self.provider == "gmail"
                else self.sendgrid_client is not None
            ),
            "from_email": (
                os.getenv('GMAIL_FROM_EMAIL') if self.provider == "gmail"
                else os.getenv('SENDGRID_FROM_EMAIL')
            )
        }
