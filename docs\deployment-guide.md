# 🚀 Deployment Guide

## Overview

This guide covers deploying the Meeting Intelligence Agent with LangChain integration and calendar attachment functionality in production environments.

## Prerequisites

### System Requirements

- **OS**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows
- **Python**: 3.8 or higher
- **Memory**: Minimum 2GB RAM, 4GB recommended
- **Storage**: 10GB free space for logs and summaries
- **Network**: Internet access for Google Cloud APIs

### Google Cloud Setup

1. **Create Google Cloud Project**
   ```bash
   gcloud projects create meeting-intelligence-prod
   gcloud config set project meeting-intelligence-prod
   ```

2. **Enable Required APIs**
   ```bash
   gcloud services enable aiplatform.googleapis.com
   gcloud services enable calendar-json.googleapis.com
   gcloud services enable drive.googleapis.com
   gcloud services enable gmail.googleapis.com
   ```

3. **Create Service Account**
   ```bash
   gcloud iam service-accounts create meeting-agent \
     --display-name="Meeting Intelligence Agent"
   
   gcloud projects add-iam-policy-binding meeting-intelligence-prod \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/aiplatform.user"
   
   gcloud iam service-accounts keys create ./keys/google-service-account.json \
     --iam-account=<EMAIL>
   ```

## Installation Methods

### Method 1: Docker Deployment (Recommended)

1. **Create Dockerfile**
   ```dockerfile
   FROM python:3.11-slim

   WORKDIR /app

   # Install system dependencies
   RUN apt-get update && apt-get install -y \
       gcc \
       g++ \
       && rm -rf /var/lib/apt/lists/*

   # Copy requirements and install Python dependencies
   COPY requirements.txt .
   RUN pip install --no-cache-dir -r requirements.txt

   # Copy application code
   COPY . .

   # Create necessary directories
   RUN mkdir -p keys meeting_summaries logging

   # Expose port
   EXPOSE 8000

   # Health check
   HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
     CMD curl -f http://localhost:8000/health || exit 1

   # Start application
   CMD ["python", "-m", "uvicorn", "src.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
   ```

2. **Create docker-compose.yml**
   ```yaml
   version: '3.8'
   
   services:
     meeting-agent:
       build: .
       ports:
         - "8000:8000"
       environment:
         - GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID}
         - VERTEX_AI_LOCATION=${VERTEX_AI_LOCATION}
       volumes:
         - ./keys:/app/keys:ro
         - ./meeting_summaries:/app/meeting_summaries
         - ./logging:/app/logging
       restart: unless-stopped
       healthcheck:
         test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
         interval: 30s
         timeout: 10s
         retries: 3
   
     mysql:
       image: mysql:8.0
       environment:
         MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
         MYSQL_DATABASE: meeting_intelligence
         MYSQL_USER: ${DB_USER}
         MYSQL_PASSWORD: ${DB_PASSWORD}
       volumes:
         - mysql_data:/var/lib/mysql
       restart: unless-stopped
   
   volumes:
     mysql_data:
   ```

3. **Deploy with Docker Compose**
   ```bash
   # Create environment file
   cp .env.example .env
   # Edit .env with your configuration
   
   # Build and start services
   docker-compose up -d
   
   # Check status
   docker-compose ps
   docker-compose logs meeting-agent
   ```

### Method 2: Systemd Service

1. **Create Service User**
   ```bash
   sudo useradd --system --create-home --shell /bin/bash meeting-agent
   sudo usermod -aG sudo meeting-agent
   ```

2. **Install Application**
   ```bash
   sudo -u meeting-agent bash
   cd /home/<USER>
   git clone <repository-url> meeting-intelligence-agent
   cd meeting-intelligence-agent
   
   python -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   
   mkdir -p keys meeting_summaries logging
   ```

3. **Create Systemd Service**
   ```ini
   # /etc/systemd/system/meeting-agent.service
   [Unit]
   Description=Meeting Intelligence Agent
   After=network.target mysql.service
   Wants=mysql.service
   
   [Service]
   Type=simple
   User=meeting-agent
   Group=meeting-agent
   WorkingDirectory=/home/<USER>/meeting-intelligence-agent
   Environment=PATH=/home/<USER>/meeting-intelligence-agent/venv/bin
   ExecStart=/home/<USER>/meeting-intelligence-agent/venv/bin/python -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000
   ExecReload=/bin/kill -HUP $MAINPID
   Restart=always
   RestartSec=10
   
   [Install]
   WantedBy=multi-user.target
   ```

4. **Enable and Start Service**
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable meeting-agent
   sudo systemctl start meeting-agent
   sudo systemctl status meeting-agent
   ```

### Method 3: Kubernetes Deployment

1. **Create Namespace**
   ```yaml
   # namespace.yaml
   apiVersion: v1
   kind: Namespace
   metadata:
     name: meeting-intelligence
   ```

2. **Create ConfigMap**
   ```yaml
   # configmap.yaml
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: meeting-agent-config
     namespace: meeting-intelligence
   data:
     GOOGLE_PROJECT_ID: "your-project-id"
     VERTEX_AI_LOCATION: "us-central1"
   ```

3. **Create Secret**
   ```yaml
   # secret.yaml
   apiVersion: v1
   kind: Secret
   metadata:
     name: meeting-agent-secrets
     namespace: meeting-intelligence
   type: Opaque
   data:
     google-service-account.json: <base64-encoded-json>
   ```

4. **Create Deployment**
   ```yaml
   # deployment.yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: meeting-agent
     namespace: meeting-intelligence
   spec:
     replicas: 2
     selector:
       matchLabels:
         app: meeting-agent
     template:
       metadata:
         labels:
           app: meeting-agent
       spec:
         containers:
         - name: meeting-agent
           image: meeting-agent:latest
           ports:
           - containerPort: 8000
           envFrom:
           - configMapRef:
               name: meeting-agent-config
           volumeMounts:
           - name: google-credentials
             mountPath: /app/keys
             readOnly: true
           livenessProbe:
             httpGet:
               path: /health
               port: 8000
             initialDelaySeconds: 30
             periodSeconds: 30
         volumes:
         - name: google-credentials
           secret:
             secretName: meeting-agent-secrets
   ```

5. **Create Service**
   ```yaml
   # service.yaml
   apiVersion: v1
   kind: Service
   metadata:
     name: meeting-agent-service
     namespace: meeting-intelligence
   spec:
     selector:
       app: meeting-agent
     ports:
     - port: 80
       targetPort: 8000
     type: LoadBalancer
   ```

## Environment Configuration

### Production .env File

```bash
# Google Cloud Configuration
GOOGLE_PROJECT_ID=meeting-intelligence-prod
GOOGLE_APPLICATION_CREDENTIALS=./keys/google-service-account.json
VERTEX_AI_LOCATION=us-central1

# Gmail API Configuration
GMAIL_CREDENTIALS_PATH=./keys/gmail-credentials.json
GMAIL_TOKEN_PATH=./keys/gmail-token.json

# Database Configuration
DB_HOST=mysql-server.internal
DB_PORT=3306
DB_NAME=meeting_intelligence
DB_USER=meeting_agent
DB_PASSWORD=secure_password_here

# Email Provider
EMAIL_PROVIDER=gmail

# Application Settings
LOG_LEVEL=INFO
ENABLE_SCHEDULER=true
TIME_WINDOW_MINUTES=30

# Security
API_KEY_HEADER=X-API-Key
ALLOWED_ORIGINS=https://your-domain.com
```

## Security Configuration

### 1. API Security

```python
# Add to src/api/main.py
from fastapi.security import HTTPBearer
from fastapi.middleware.cors import CORSMiddleware

security = HTTPBearer()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://your-domain.com"],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)
```

### 2. Firewall Rules

```bash
# Allow only necessary ports
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 8000/tcp  # API
sudo ufw enable
```

### 3. SSL/TLS Configuration

```nginx
# /etc/nginx/sites-available/meeting-agent
server {
    listen 443 ssl http2;
    server_name api.your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Monitoring and Logging

### 1. Application Monitoring

```python
# Add to requirements.txt
prometheus-client>=0.17.0
```

```python
# Add to src/api/main.py
from prometheus_client import Counter, Histogram, generate_latest

workflow_executions = Counter('workflow_executions_total', 'Total workflow executions')
response_time = Histogram('response_time_seconds', 'Response time')

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type="text/plain")
```

### 2. Log Aggregation

```yaml
# docker-compose.yml addition
  fluentd:
    image: fluent/fluentd:v1.16
    volumes:
      - ./logging:/fluentd/log
      - ./fluentd.conf:/fluentd/etc/fluent.conf
    ports:
      - "24224:24224"
```

### 3. Health Checks

```bash
# Create health check script
#!/bin/bash
# health-check.sh

HEALTH_URL="http://localhost:8000/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy (HTTP $RESPONSE)"
    exit 1
fi
```

## Backup and Recovery

### 1. Database Backup

```bash
#!/bin/bash
# backup-db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/mysql"
mkdir -p $BACKUP_DIR

mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASSWORD meeting_intelligence > $BACKUP_DIR/meeting_intelligence_$DATE.sql

# Keep only last 7 days
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
```

### 2. File Backup

```bash
#!/bin/bash
# backup-files.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/files"
mkdir -p $BACKUP_DIR

tar -czf $BACKUP_DIR/meeting_summaries_$DATE.tar.gz meeting_summaries/
tar -czf $BACKUP_DIR/keys_$DATE.tar.gz keys/

# Keep only last 30 days
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

## Performance Optimization

### 1. Database Optimization

```sql
-- Add indexes for better performance
CREATE INDEX idx_meeting_date ON meetings(meeting_date);
CREATE INDEX idx_processed_status ON meetings(processed);
```

### 2. Caching

```python
# Add Redis caching
import redis
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expiration=300):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
            result = func(*args, **kwargs)
            redis_client.setex(cache_key, expiration, json.dumps(result))
            return result
        return wrapper
    return decorator
```

## Troubleshooting

### Common Issues

1. **Service Won't Start**
   ```bash
   # Check logs
   sudo journalctl -u meeting-agent -f
   
   # Check port availability
   sudo netstat -tlnp | grep :8000
   ```

2. **Google API Errors**
   ```bash
   # Verify credentials
   gcloud auth application-default print-access-token
   
   # Check API quotas
   gcloud logging read "resource.type=gce_instance" --limit=50
   ```

3. **Database Connection Issues**
   ```bash
   # Test database connection
   mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD -e "SELECT 1"
   ```

### Performance Issues

1. **High Memory Usage**
   - Increase container memory limits
   - Implement result caching
   - Optimize LangChain memory usage

2. **Slow API Responses**
   - Add database indexes
   - Implement async processing
   - Use connection pooling

## Scaling Considerations

### Horizontal Scaling

- Deploy multiple instances behind a load balancer
- Use shared database and Redis cache
- Implement distributed task queue

### Vertical Scaling

- Increase CPU and memory resources
- Optimize database queries
- Use faster storage (SSD)

This deployment guide provides comprehensive instructions for production deployment of the Meeting Intelligence Agent with LangChain integration and calendar attachment functionality.
