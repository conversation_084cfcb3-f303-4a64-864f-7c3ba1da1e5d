"""AI service for summarizing meeting transcripts using Vertex AI and Gemini."""

import json
from typing import Dict, Optional, List, Any
from datetime import datetime, timezone
from dataclasses import dataclass, asdict

import structlog

# LangChain imports for AI models
try:
    from langchain_google_vertexai import ChatVertexAI
    from langchain_core.messages import HumanMessage, SystemMessage
    from langchain_core.prompts import ChatPromptTemplate
    AI_AVAILABLE = True
except ImportError as e:
    ChatVertexAI = None
    HumanMessage = None
    SystemMessage = None
    ChatPromptTemplate = None
    AI_AVAILABLE = False

# Optional fallback AI imports (for backward compatibility)
try:
    import google.generativeai as genai
    from google.cloud import aiplatform
    from vertexai.generative_models import GenerativeModel
    FALLBACK_AI_AVAILABLE = True
except ImportError as e:
    genai = None
    aiplatform = None
    GenerativeModel = None
    FALLBACK_AI_AVAILABLE = False

# Configuration removed - using environment variables directly
from pathlib import Path

logger = structlog.get_logger(__name__)


@dataclass
class TranscriptData:
    """Data class for transcript information."""
    file_path: Path
    content: str
    metadata: Dict[str, Any]

    def get_summary_text(self) -> str:
        """Get the transcript content for summarization."""
        return self.content


# Create a settings-like object for backward compatibility
class Settings:
    def __init__(self):
        import os
        self._project_id = os.getenv("GOOGLE_CLOUD_PROJECT_ID", "meeting-intelligence-agent")
        self._location = os.getenv("VERTEX_AI_LOCATION", "us-central1")

    @property
    def email_subject_prefix(self):
        return "Meeting Summary:"

    @property
    def google_cloud_project_id(self):
        return self._project_id

    @property
    def vertex_ai_location(self):
        return self._location

    @property
    def gemini_api_key(self):
        import os
        return os.getenv("GEMINI_API_KEY")

    @property
    def upload_summaries_to_drive(self):
        import os
        return os.getenv("UPLOAD_SUMMARIES_TO_DRIVE", "false").lower() == "true"

settings = Settings()


@dataclass
class MeetingData:
    """Enhanced data class for comprehensive meeting debrief information."""
    meeting_metadata: Dict[str, Any]
    executive_summary: str
    outcomes: List[Dict[str, Any]]
    open_questions: List[Dict[str, Any]]
    working_sessions_needed: List[Dict[str, Any]]
    html_file_path: str = ""
    json_file_path: str = ""

    # Backward compatibility properties
    @property
    def action_items(self) -> List[Dict[str, Any]]:
        """Backward compatibility: map outcomes to action_items."""
        return self.outcomes

    @property
    def key_decisions(self) -> List[Dict[str, Any]]:
        """Backward compatibility: map outcomes to key_decisions."""
        return self.outcomes


class MeetingSummary:
    """Represents a meeting summary with enhanced debrief data and HTML formats."""

    def __init__(self, meeting_data: MeetingData, html_summary: str, transcript_data: TranscriptData):
        self.meeting_data = meeting_data
        self.html_summary = html_summary
        self.transcript_data = transcript_data

        # Backward compatibility - expose as json_summary
        self.json_summary = asdict(meeting_data)

        self.metadata = {
            'source_file': transcript_data.file_path.name,
            'word_count': transcript_data.metadata.get('word_count', 0),
            'participants': meeting_data.meeting_metadata.get('attendees', []),
            'meeting_time': transcript_data.metadata.get('meeting_time')
        }

    def get_email_subject(self) -> str:
        """Generate email subject for the summary."""
        meeting_title = self.meeting_data.meeting_metadata.get('title', 'Meeting Debrief')
        return f"{settings.email_subject_prefix} {meeting_title}"

    def get_participants_summary(self) -> str:
        """Get formatted participants list."""
        participants = self.meeting_data.meeting_metadata.get('attendees', [])
        if participants:
            return f"Participants: {', '.join(participants)}"
        return "Participants: Not specified"


class AISummarizer:
    """Service for AI-powered meeting transcript summarization using LangChain."""

    def __init__(self):
        self.langchain_llm = None
        self.vertex_model = None  # Fallback
        self.gemini_model = None  # Fallback
        self.ai_available = AI_AVAILABLE
        self.settings = settings
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize LangChain AI models."""
        if not AI_AVAILABLE:
            raise ValueError("LangChain AI libraries not available. Please install with: pip install -r requirements.txt")

        try:
            # Initialize LangChain ChatVertexAI (primary model)
            import os
            project_id = os.getenv('GOOGLE_PROJECT_ID', settings._project_id)
            location = os.getenv('VERTEX_AI_LOCATION', settings._location)

            if project_id and ChatVertexAI:
                self.langchain_llm = ChatVertexAI(
                    model_name="gemini-2.0-flash-001",
                    project=project_id,
                    location=location,
                    temperature=0.1,  # Low temperature for consistent, factual summaries
                    max_output_tokens=4096,
                    top_p=0.8,
                    top_k=40,
                    verbose=False  # Reduce noise in logs
                )
                logger.info("LangChain ChatVertexAI model initialized")

            # Initialize fallback models if LangChain fails
            if FALLBACK_AI_AVAILABLE:
                try:
                    # Initialize Vertex AI fallback
                    if project_id and aiplatform:
                        aiplatform.init(project=project_id, location=location)
                        self.vertex_model = GenerativeModel("gemini-1.5-pro")
                        logger.info("Vertex AI Gemini fallback model initialized")

                    # Initialize Gemini API fallback
                    if settings.gemini_api_key and genai:
                        genai.configure(api_key=settings.gemini_api_key)
                        self.gemini_model = genai.GenerativeModel('gemini-1.5-pro')
                        logger.info("Gemini API fallback model initialized")
                except Exception as fallback_error:
                    logger.warning(f"Failed to initialize fallback models: {fallback_error}")

            if not self.langchain_llm and not self.vertex_model and not self.gemini_model:
                raise ValueError("No AI models available. Please configure Vertex AI or Gemini API.")

        except Exception as error:
            logger.error(f"Failed to initialize AI models: {error}")
            raise
    
    def summarize_transcript(self, transcript_data: TranscriptData) -> MeetingSummary:
        """
        Generate comprehensive meeting debrief from transcript using enhanced AI prompting.

        Args:
            transcript_data: Processed transcript data

        Returns:
            MeetingSummary object with structured debrief data and HTML
        """
        logger.info(f"Generating AI debrief for transcript: {transcript_data.file_path.name}")

        try:
            # Generate structured meeting debrief
            meeting_data = self._generate_meeting_debrief(transcript_data)

            # Generate professional HTML
            html_summary = self._generate_professional_html(meeting_data)

            # Save files locally
            self._save_summary_files(meeting_data, html_summary, transcript_data)

            summary = MeetingSummary(meeting_data, html_summary, transcript_data)
            logger.info("Successfully generated meeting debrief")

            return summary

        except Exception as error:
            logger.error(f"Failed to generate debrief: {error}")
            raise
    
    def _generate_meeting_debrief(self, transcript_data: TranscriptData) -> MeetingData:
        """Generate structured meeting debrief using enhanced AI prompting."""
        prompt = self._get_debrief_prompt(transcript_data)

        try:
            # Try LangChain structured approach first, fall back to regular call
            if self.langchain_llm:
                response = self._call_langchain_structured(prompt)
            else:
                response = self._call_ai_model(prompt)
            response_text = response.strip()

            # Debug logging
            logger.info(f"Raw AI response length: {len(response_text)} characters")
            logger.debug(f"Raw AI response (first 500 chars): {response_text[:500]}")

            # Clean up the response text - remove markdown formatting if present
            original_response = response_text
            if response_text.startswith('```json'):
                response_text = response_text[7:]  # Remove ```json
            elif response_text.startswith('```'):
                response_text = response_text[3:]  # Remove ```
            if response_text.endswith('```'):
                response_text = response_text[:-3]  # Remove ```

            response_text = response_text.strip()

            # Try to find JSON content between curly braces
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                logger.error(f"No JSON object found in response. Full response: {original_response[:1000]}")
                raise ValueError("No JSON object found in response")

            json_str = response_text[start_idx:end_idx]
            logger.info(f"Extracted JSON string length: {len(json_str)} characters")
            logger.debug(f"Extracted JSON string (first 200 chars): {json_str[:200]}")

            try:
                meeting_data_dict = json.loads(json_str)
                logger.info("Successfully parsed JSON response")
            except json.JSONDecodeError as e:
                logger.error(f"JSON Parse Error: {e}")
                logger.error(f"Error location: {e.pos if hasattr(e, 'pos') else 'unknown'}")
                logger.error(f"Problematic part: {json_str[max(0, e.pos-20):e.pos+20] if hasattr(e, 'pos') else 'unknown'}")
                logger.error(f"Full JSON string: {json_str}")
                raise

            # Ensure date_processed_utc is set
            if "meeting_metadata" in meeting_data_dict:
                meeting_data_dict["meeting_metadata"]["date_processed_utc"] = datetime.now(timezone.utc).isoformat()

            return MeetingData(**meeting_data_dict)

        except Exception as error:
            logger.error(f"Failed to parse debrief response: {error}")
            # Return fallback structured data
            return self._create_fallback_meeting_data(transcript_data)
    
    def _generate_professional_html(self, meeting_data: MeetingData) -> str:
        """Generate professional HTML report from meeting data."""
        return self._create_professional_html_template(meeting_data)

    def _save_summary_files(self, meeting_data: MeetingData, html_summary: str, transcript_data: TranscriptData):
        """Save HTML and JSON summary files locally and optionally upload to Drive."""
        from pathlib import Path
        import json
        from datetime import datetime

        # Create output directory
        output_dir = Path("meeting_summaries")
        output_dir.mkdir(exist_ok=True)

        # Generate filename based on meeting title and timestamp
        meeting_title = meeting_data.meeting_metadata.get('title', 'Meeting')
        # Clean filename - remove invalid characters
        clean_title = "".join(c for c in meeting_title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        clean_title = clean_title.replace(' ', '_')

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"{clean_title}_{timestamp}"

        # Save HTML file
        html_file = output_dir / f"{base_filename}.html"
        try:
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_summary)
            logger.info(f"Saved HTML summary to: {html_file}")
        except Exception as e:
            logger.error(f"Failed to save HTML file: {e}")

        # Save JSON file
        json_file = output_dir / f"{base_filename}.json"
        try:
            json_data = {
                "meeting_data": asdict(meeting_data),
                "transcript_metadata": {
                    "source_file": transcript_data.file_path.name,
                    "word_count": transcript_data.metadata.get('word_count', 0),
                    "processing_timestamp": datetime.now().isoformat()
                }
            }
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved JSON summary to: {json_file}")
        except Exception as e:
            logger.error(f"Failed to save JSON file: {e}")

        # Store file paths in meeting data for email attachment
        meeting_data.html_file_path = str(html_file)
        meeting_data.json_file_path = str(json_file)

        # Upload to Google Drive if enabled
        if settings.upload_summaries_to_drive:
            try:
                # TODO: Implement DriveUploadService or use existing Google Drive utilities
                logger.info("Drive upload is enabled but DriveUploadService is not yet implemented")
                logger.info(f"Files saved locally: HTML={html_file}, JSON={json_file}")

            except Exception as e:
                logger.error(f"Failed to upload summaries to Drive: {e}")
                # Don't fail the entire process if Drive upload fails
    
    def _call_ai_model(self, prompt: str) -> str:
        """Call available AI model with the given prompt, prioritizing LangChain."""
        last_error = None

        # Try LangChain ChatVertexAI first (primary method)
        if self.langchain_llm:
            try:
                logger.info("Attempting to use LangChain ChatVertexAI for content generation")

                # Create a structured prompt for better results
                messages = [
                    SystemMessage(content="You are a skilled meeting analyst who specializes in extracting actionable insights from business conversations."),
                    HumanMessage(content=prompt)
                ]

                response = self.langchain_llm.invoke(messages)
                if response and response.content:
                    logger.info("Successfully generated content using LangChain ChatVertexAI")
                    return response.content
                else:
                    raise ValueError("Empty response from LangChain ChatVertexAI")
            except Exception as error:
                logger.warning(f"LangChain ChatVertexAI call failed: {error}")
                last_error = error

        # Try Gemini API as fallback
        if self.gemini_model:
            try:
                logger.info("Attempting to use Gemini API for content generation (fallback)")
                response = self.gemini_model.generate_content(prompt)
                if response and response.text:
                    logger.info("Successfully generated content using Gemini API")
                    return response.text
                else:
                    raise ValueError("Empty response from Gemini API")
            except Exception as error:
                logger.warning(f"Gemini API call failed: {error}")
                last_error = error

        # Try Vertex AI as final fallback
        if self.vertex_model:
            try:
                logger.info("Attempting to use Vertex AI for content generation (final fallback)")
                response = self.vertex_model.generate_content(prompt)
                if response and response.text:
                    logger.info("Successfully generated content using Vertex AI")
                    return response.text
                else:
                    raise ValueError("Empty response from Vertex AI")
            except Exception as error:
                logger.warning(f"Vertex AI call failed: {error}")
                last_error = error

        # If all failed, raise the last error
        if last_error:
            logger.error(f"All AI models failed. Last error: {last_error}")
            raise last_error
        else:
            raise ValueError("No AI models available")

    def _call_langchain_structured(self, prompt: str) -> str:
        """
        Call LangChain with structured output for better JSON parsing.
        This method uses LangChain's built-in JSON mode for more reliable structured output.
        """
        if not self.langchain_llm:
            raise ValueError("LangChain model not available")

        try:
            logger.info("Attempting structured output with LangChain ChatVertexAI")

            # Create a chat prompt template for better structure
            chat_prompt = ChatPromptTemplate.from_messages([
                ("system", "You are a skilled meeting analyst. Always respond with valid JSON in the exact format requested."),
                ("human", prompt)
            ])

            # Create a chain with the prompt and model
            chain = chat_prompt | self.langchain_llm

            # Invoke the chain
            response = chain.invoke({})

            if response and response.content:
                logger.info("Successfully generated structured content using LangChain")
                return response.content
            else:
                raise ValueError("Empty response from LangChain structured call")

        except Exception as error:
            logger.warning(f"LangChain structured call failed: {error}")
            # Fall back to regular call
            return self._call_ai_model(prompt)

    def _get_debrief_prompt(self, transcript_data: TranscriptData) -> str:
        """Get enhanced prompt for meeting debrief generation."""
        return f"""You are a skilled meeting analyst who specializes in extracting actionable insights from business conversations. Your role is to carefully listen to what was actually discussed and create a professional summary that captures the real decisions, commitments, and next steps.

## Your Core Principles

**Accuracy First**: Only include information that was genuinely discussed in the meeting. If someone didn't explicitly commit to something, don't assume they did. If a deadline wasn't mentioned, acknowledge that it's unspecified rather than guessing.

**Context Awareness**: Pay attention to the flow of conversation. Understand when someone is making a firm commitment versus just brainstorming ideas. Notice when questions are raised but left unresolved, and when follow-up discussions are needed.

**Natural Language**: Write summaries that sound professional and human, not robotic. Use clear, business-appropriate language that meeting participants would recognize as accurate.

## What to Extract

**Firm Decisions & Commitments**: Look for moments where the group reached consensus or where individuals made clear commitments. These might sound like:
- "Let's go with option A"
- "I'll handle the client presentation"
- "We've decided to postpone the launch"
- "Sarah will take the lead on this"

**Actionable Next Steps**: Capture specific tasks that people committed to, not general suggestions. Listen for:
- Direct assignments: "John, can you send me those numbers?"
- Personal commitments: "I'll follow up with the vendor"
- Group agreements: "We need to schedule a follow-up meeting"

**Unresolved Questions**: Notice when important topics were raised but not settled:
- Questions that sparked discussion but no clear answer
- Decisions that were deferred to later
- Issues that need more research or input

**Follow-up Needs**: Identify when the group recognized they need additional meetings or sessions:
- Technical deep-dives that were mentioned
- Strategy sessions that were proposed
- One-on-one conversations that were suggested

## Response Format

Provide your analysis as a clean JSON object with no additional text or formatting:

{{
    "meeting_metadata": {{
        "title": "string", // Extract or infer the meeting's purpose from context
        "attendees": ["string"], // Names of people who spoke or were mentioned as present
        "absent": ["string"], // People mentioned as absent, or empty array if none noted
        "date_processed_utc": "ISO 8601 string" // Current timestamp in UTC format
    }},
    "executive_summary": "string", // A concise 2-3 sentence summary of the meeting's purpose and key outcomes in natural business language
    "outcomes": [
        {{
            "decision": "string", // What was actually decided, in clear business language
            "owner": "string", // Who is primarily accountable for this outcome
            "rationale": "string", // The reasoning behind this decision as discussed
            "context": "string", // A relevant quote or paraphrase that supports this outcome
            "actions": [
                {{
                    "owner": "string", // The specific person who committed to this task
                    "task": "string", // What they committed to do, in actionable terms
                    "deadline": "string", // Exact deadline if mentioned, otherwise "Not specified"
                    "priority": "string" // HIGH/MEDIUM/LOW based on urgency expressed in discussion
                }}
            ]
        }}
    ],
    "open_questions": [
        {{
            "question": "string", // The unresolved question or issue, stated naturally
            "owner": "string", // Who was asked to investigate, or "Unassigned" if unclear
            "status": "string" // Current state: "Needs research", "Awaiting decision", "Deferred", etc.
        }}
    ],
    "working_sessions_needed": [
        {{
            "topic": "string", // The subject that needs a dedicated session
            "participants": "string", // Who should be involved in this session
            "goal": "string" // What this session should accomplish
        }}
    ]
}}

## Quality Guidelines

**Executive Summary**: Keep this brief and focused - just 2-3 sentences that capture the meeting's main purpose and key outcomes. Write as if giving a quick verbal update to someone who missed the meeting. Use natural business language and avoid unnecessary detail.

**Outcomes & Actions**: Only include decisions that were actually made and tasks that people genuinely committed to. If someone said "maybe I could look into that," that's not a firm commitment. If they said "I'll send you the report by Friday," that's a clear action item.

**Open Questions**: These should be substantive issues that the group recognized need resolution, not minor clarifications. Look for moments where someone said "we need to figure out..." or "that's a good question, let me research..."

**Working Sessions**: Only include follow-up meetings that were specifically discussed as needed, not general suggestions about "staying in touch."

**Language Quality**: Use professional, natural language throughout. Avoid corporate jargon unless it was actually used in the meeting. Write as a skilled business professional would communicate.

---

**TRANSCRIPT TO ANALYZE:**

{transcript_data.get_summary_text()}

---

**IMPORTANT**: Return only the JSON object above, with no additional text, explanations, or formatting. Ensure all strings use natural, professional language that accurately reflects what was discussed."""
    

    
    def _create_fallback_meeting_data(self, transcript_data: TranscriptData) -> MeetingData:
        """Create fallback meeting data when AI parsing fails."""
        # Try to extract basic information from transcript
        transcript_text = transcript_data.get_summary_text()
        participants = transcript_data.metadata.get('participants', [])

        # Create a basic summary from the transcript
        word_count = len(transcript_text.split())
        basic_summary = f"This meeting transcript contains {word_count} words of discussion"
        if participants:
            basic_summary += f" among {len(participants)} participants: {', '.join(participants[:3])}"
            if len(participants) > 3:
                basic_summary += f" and {len(participants) - 3} others"
        basic_summary += ". The AI summarization service encountered an issue processing this transcript, but the raw content is available for manual review."

        return MeetingData(
            meeting_metadata={
                "title": f"Meeting Debrief - {transcript_data.file_path.stem}",
                "attendees": participants,
                "absent": [],
                "date_processed_utc": datetime.now(timezone.utc).isoformat()
            },
            executive_summary=basic_summary,
            outcomes=[{
                "decision": "Manual review required",
                "owner": "Meeting organizer",
                "rationale": "AI processing encountered an issue with this transcript",
                "context": "Please review the original transcript for detailed meeting content",
                "actions": [{
                    "owner": "Meeting organizer",
                    "task": "Review transcript and extract key decisions manually",
                    "deadline": "Not specified",
                    "priority": "MEDIUM"
                }]
            }],
            open_questions=[{
                "question": "What were the key decisions and action items from this meeting?",
                "owner": "Meeting organizer",
                "status": "Requires manual transcript review"
            }],
            working_sessions_needed=[]
        )

    def _create_professional_html_template(self, meeting_data: MeetingData) -> str:
        """Create a professional HTML template that matches the design shown in the images."""
        # Extract data
        metadata = meeting_data.meeting_metadata
        title = metadata.get('title', 'Meeting Debrief')
        attendees = metadata.get('attendees', [])
        absent = metadata.get('absent', [])
        date_processed = metadata.get('date_processed_utc', datetime.now().isoformat())

        executive_summary = meeting_data.executive_summary
        outcomes = meeting_data.outcomes
        open_questions = meeting_data.open_questions
        working_sessions = meeting_data.working_sessions_needed

        # Generate tasks by owner
        all_actions = []
        for outcome in outcomes:
            all_actions.extend(outcome.get('actions', []))

        # Group tasks by owner
        tasks_by_owner = {}
        for action in all_actions:
            owner = action.get('owner', 'Unassigned')
            if owner not in tasks_by_owner:
                tasks_by_owner[owner] = []
            tasks_by_owner[owner].append(action)

        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title} - Meeting Debrief</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }}
        .container {{
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }}
        h1 {{
            color: #1a1a1a;
            margin-bottom: 8px;
            font-size: 28px;
            font-weight: 600;
        }}
        .subtitle {{
            color: #666;
            font-size: 14px;
            margin-bottom: 24px;
        }}
        .metadata {{
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 32px;
            border: 1px solid #e9ecef;
        }}
        .metadata p {{
            margin: 4px 0;
            font-size: 14px;
        }}
        .metadata strong {{
            font-weight: 600;
        }}
        h2 {{
            color: #1a1a1a;
            font-size: 20px;
            font-weight: 600;
            margin: 32px 0 16px 0;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }}
        .summary {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin-bottom: 24px;
        }}
        .outcome {{
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
        }}
        .outcome-title {{
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
        }}
        .outcome-meta {{
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }}
        .context {{
            font-style: italic;
            color: #666;
            font-size: 14px;
            border-left: 3px solid #e9ecef;
            padding-left: 12px;
            margin-top: 12px;
        }}
        .owner-section {{
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;
        }}
        .owner-header {{
            background: #f8f9fa;
            padding: 16px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .owner-name {{
            font-weight: 600;
            color: #1a1a1a;
        }}
        .task-count {{
            font-size: 12px;
            color: #666;
        }}
        .tasks-table {{
            width: 100%;
            border-collapse: collapse;
        }}
        .tasks-table th {{
            background: #f8f9fa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #e9ecef;
        }}
        .tasks-table td {{
            padding: 12px 16px;
            border-bottom: 1px solid #f1f3f4;
            font-size: 14px;
        }}
        .priority {{
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }}
        .priority-high {{
            background: #fee;
            color: #c53030;
        }}
        .priority-medium {{
            background: #fffbeb;
            color: #d69e2e;
        }}
        .priority-low {{
            background: #f0f9ff;
            color: #2b6cb0;
        }}
        .question {{
            background: #fffbeb;
            border: 1px solid #fed7aa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }}
        .session {{
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }}
        .no-items {{
            color: #666;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{title}</h1>
        <div class="subtitle">Debrief generated on {datetime.fromisoformat(date_processed.replace('Z', '+00:00')).strftime('%m/%d/%Y, %I:%M:%S %p')}</div>

        <div class="metadata">
            <p><strong>Present:</strong> {', '.join(attendees) if attendees else 'None specified'}</p>
            <p><strong>Absent:</strong> {', '.join(absent) if absent else 'None'}</p>
        </div>

        <h2>Executive Summary</h2>
        <div class="summary">
            {executive_summary or 'No executive summary provided.'}
        </div>

        <h2>Key Outcomes</h2>
        {self._generate_outcomes_html(outcomes)}

        <h2>Tasks</h2>
        {self._generate_tasks_html(tasks_by_owner)}

        <h2>Open Questions</h2>
        {self._generate_questions_html(open_questions)}

        <h2>Working Sessions Needed</h2>
        {self._generate_sessions_html(working_sessions)}
    </div>
</body>
</html>"""

    def _generate_outcomes_html(self, outcomes):
        """Generate HTML for outcomes section."""
        if not outcomes:
            return '<div class="no-items">No outcomes recorded.</div>'

        html = ""
        for outcome in outcomes:
            context_html = ""
            if outcome.get('context'):
                context_html = f'<div class="context"><strong>Context:</strong> {outcome["context"]}</div>'

            html += f"""
            <div class="outcome">
                <div class="outcome-title">{outcome.get('decision', 'No decision specified')}</div>
                <div class="outcome-meta">
                    <strong>Owner:</strong> {outcome.get('owner', 'Unassigned')} |
                    <strong>Rationale:</strong> {outcome.get('rationale', 'No rationale provided')}
                </div>
                {context_html}
            </div>
            """
        return html

    def _generate_tasks_html(self, tasks_by_owner):
        """Generate HTML for tasks section grouped by owner."""
        if not tasks_by_owner:
            return '<div class="no-items">No tasks assigned.</div>'

        html = ""
        for owner, tasks in tasks_by_owner.items():
            high_priority_count = sum(1 for task in tasks if task.get('priority') == 'HIGH')

            tasks_rows = ""
            for task in tasks:
                priority = task.get('priority', 'MEDIUM')
                priority_class = f"priority priority-{priority.lower()}"

                tasks_rows += f"""
                <tr>
                    <td>{task.get('task', 'No task description')}</td>
                    <td>{task.get('deadline', 'Not Specified')}</td>
                    <td><span class="{priority_class}">{priority}</span></td>
                </tr>
                """

            html += f"""
            <div class="owner-section">
                <div class="owner-header">
                    <div class="owner-name">{owner}</div>
                    <div class="task-count">{len(tasks)} tasks, {high_priority_count} high priority</div>
                </div>
                <table class="tasks-table">
                    <thead>
                        <tr>
                            <th>Task</th>
                            <th>Deadline</th>
                            <th>Priority</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tasks_rows}
                    </tbody>
                </table>
            </div>
            """
        return html

    def _generate_questions_html(self, questions):
        """Generate HTML for open questions section."""
        if not questions:
            return '<div class="no-items">No open questions were left unresolved.</div>'

        html = ""
        for question in questions:
            html += f"""
            <div class="question">
                <div style="font-weight: 600; margin-bottom: 8px;">{question.get('question', 'No question specified')}</div>
                <div style="font-size: 14px; color: #666;">
                    <strong>Owner:</strong> {question.get('owner', 'Unassigned')} |
                    <strong>Status:</strong> {question.get('status', 'Pending')}
                </div>
            </div>
            """
        return html

    def _generate_sessions_html(self, sessions):
        """Generate HTML for working sessions section."""
        if not sessions:
            return '<div class="no-items">No working sessions needed.</div>'

        html = ""
        for session in sessions:
            html += f"""
            <div class="session">
                <div style="font-weight: 600; margin-bottom: 8px;">{session.get('topic', 'No topic specified')}</div>
                <div style="font-size: 14px; color: #666; margin-bottom: 4px;">
                    <strong>Participants:</strong> {session.get('participants', 'Not specified')}
                </div>
                <div style="font-size: 14px; color: #666;">
                    <strong>Goal:</strong> {session.get('goal', 'No goal specified')}
                </div>
            </div>
            """
        return html
