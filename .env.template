# Meeting Intelligence Agent Configuration
# Technology Stack: FastAPI + MySQL + <PERSON><PERSON><PERSON><PERSON> + Vertex AI

# Google Cloud Configuration
GOOGLE_PROJECT_ID=demo-project-id
GOOGLE_CREDENTIALS_PATH=./keys/google-service-account.json

# Vertex AI Configuration (ONLY LLM Provider)
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_MODEL=gemini-pro

# MySQL Database Configuration (for demo - will skip DB operations if not available)
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USERNAME=root
MYSQL_PASSWORD=demo-password
MYSQL_DATABASE=meeting_intelligence

# Gmail API Configuration
GMAIL_CREDENTIALS_PATH=./keys/gmail-credentials.json
GMAIL_TOKEN_PATH=./keys/gmail-token.json
GMAIL_FROM_EMAIL=<EMAIL>

# Email Provider (gmail or sendgrid)
EMAIL_PROVIDER=gmail

# SendGrid (alternative to Gmail)
SENDGRID_API_KEY=demo-sendgrid-key
SENDGRID_FROM_EMAIL=<EMAIL>

# Agent Configuration
OUTPUT_DIR=./output
ENABLE_DEDUPLICATION=true
MAX_RETRIES=3
DEBUG_MODE=true
REFLECTION_THRESHOLD=10
SIMILARITY_THRESHOLD=0.85

# FastAPI Server Configuration
HOST=0.0.0.0
PORT=8000
