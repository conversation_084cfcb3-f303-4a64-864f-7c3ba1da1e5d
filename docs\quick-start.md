# 🚀 Quick Start Guide

Get the Meeting Intelligence Agent with <PERSON><PERSON>hain integration running in under 10 minutes!

## 📋 Prerequisites Checklist

Before starting, ensure you have:

- [ ] Python 3.8+ installed
- [ ] Google Cloud Project created
- [ ] Google Workspace account (for Calendar/Drive access)
- [ ] Git installed

## ⚡ 5-Minute Setup

### Step 1: <PERSON>lone and Install (2 minutes)

```bash
# Clone the repository
git clone <repository-url>
cd meeting-intelligence-agent

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Create directories
mkdir -p keys meeting_summaries logging
```

### Step 2: Google Cloud Setup (2 minutes)

1. **Enable APIs** (30 seconds)
   ```bash
   gcloud services enable aiplatform.googleapis.com
   gcloud services enable calendar-json.googleapis.com
   gcloud services enable drive.googleapis.com
   ```

2. **Create Service Account** (90 seconds)
   ```bash
   gcloud iam service-accounts create meeting-agent
   gcloud iam service-accounts keys create ./keys/google-service-account.json \
     --iam-account=<EMAIL>
   ```

### Step 3: Environment Configuration (1 minute)

Create `.env` file:
```bash
# Copy template
cp .env.example .env

# Edit with your details
GOOGLE_PROJECT_ID=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=./keys/google-service-account.json
VERTEX_AI_LOCATION=us-central1
```

### Step 4: Test Run (30 seconds)

```bash
# Start the API server
python -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000

# Test in another terminal
curl http://localhost:8000/health
```

## 🎯 First Workflow Execution

### Option 1: Automatic Workflow

```bash
# Trigger the complete 6-step workflow
curl -X POST http://localhost:8000/agent/trigger-workflow
```

### Option 2: Interactive Chat

```bash
# Chat with the LangChain agent
curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Execute the Post meetingworkflow"}'
```

### Option 3: Manual Steps

```bash
# Step-by-step execution
curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Find meetings from the last 30 minutes"}'

curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Summarize the latest meeting transcript"}'
```

## 🔧 Quick Configuration

### Essential Settings

```bash
# .env file essentials
GOOGLE_PROJECT_ID=your-project-id                    # Required
GOOGLE_APPLICATION_CREDENTIALS=./keys/service.json   # Required
VERTEX_AI_LOCATION=us-central1                       # Required
EMAIL_PROVIDER=gmail                                 # Optional
```

### Gmail Setup (Optional)

For email notifications:
```bash
# Download Gmail credentials from Google Cloud Console
# Save as ./keys/gmail-credentials.json
GMAIL_CREDENTIALS_PATH=./keys/gmail-credentials.json
```

## 📊 Verify Installation

### Health Check

```bash
curl http://localhost:8000/health
```

Expected response:
```json
{
  "status": "healthy",
  "services": {
    "langchain_agent": "initialized",
    "google_auth": "authenticated"
  }
}
```

### Workflow Status

```bash
curl http://localhost:8000/agent/workflow-status
```

Expected response:
```json
{
  "agent_status": "ready",
  "tools_available": 6,
  "tools": [
    "calendar_tool",
    "drive_tool",
    "summarizer_tool", 
    "notification_tool",
    "calendar_attachment_tool",
    "file_manager_tool"
  ]
}
```

## 🎮 Interactive Demo

### Chat Commands to Try

```bash
# Basic workflow
curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "What tools do you have available?"}'

# Meeting search
curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Find any meetings from today"}'

# File operations
curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "List files in the meeting_summaries directory"}'
```

## 🚀 Production Quick Start

### Docker (Recommended)

```bash
# Create docker-compose.yml
cat > docker-compose.yml << EOF
version: '3.8'
services:
  meeting-agent:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./keys:/app/keys:ro
      - ./meeting_summaries:/app/meeting_summaries
    environment:
      - GOOGLE_PROJECT_ID=your-project-id
EOF

# Start with Docker
docker-compose up -d
```

### Systemd Service

```bash
# Create service file
sudo tee /etc/systemd/system/meeting-agent.service << EOF
[Unit]
Description=Meeting Intelligence Agent
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)
ExecStart=$(which python) -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# Enable and start
sudo systemctl enable meeting-agent
sudo systemctl start meeting-agent
```

## 🔍 Troubleshooting Quick Fixes

### Common Issues

**❌ "Authentication failed"**
```bash
# Check service account file
ls -la keys/google-service-account.json
# Verify project ID
gcloud config get-value project
```

**❌ "No module named 'src'"**
```bash
# Ensure you're in the project root
pwd
# Should end with: meeting-intelligence-agent
```

**❌ "Port 8000 already in use"**
```bash
# Use different port
python -m uvicorn src.api.main:app --port 8001
```

**❌ "LangChain agent not initialized"**
```bash
# Check dependencies
pip install langchain langchain-google-vertexai
```

### Quick Diagnostics

```bash
# Check Python version
python --version  # Should be 3.8+

# Check dependencies
pip list | grep -E "(langchain|fastapi|google)"

# Check file permissions
ls -la keys/

# Check logs
tail -f logging/app.log
```

## 📱 Web Interface

Visit these URLs after starting the server:

- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Workflow Status**: http://localhost:8000/agent/workflow-status

## 🎯 Next Steps

After successful setup:

1. **📅 Calendar Integration**: Share your calendar with the service account
2. **📧 Email Setup**: Configure Gmail API for notifications
3. **🔄 Automation**: Enable the scheduler for automatic processing
4. **📊 Monitoring**: Set up logging and health checks
5. **🚀 Production**: Follow the [deployment guide](deployment-guide.md)

## 📚 Learn More

- **[Complete README](readme.md)** - Full documentation
- **[API Reference](api-documentation.md)** - Detailed API docs
- **[Deployment Guide](deployment-guide.md)** - Production setup
- **[Changelog](../CHANGELOG.md)** - Version history

## 💡 Pro Tips

### Development
```bash
# Auto-reload during development
python -m uvicorn src.api.main:app --reload --port 8000

# Debug mode
export LOG_LEVEL=DEBUG
```

### Testing
```bash
# Test specific workflow steps
curl -X POST "http://localhost:8000/agent/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Test the calendar tool"}'
```

### Monitoring
```bash
# Watch logs in real-time
tail -f logging/app.log

# Monitor API requests
curl http://localhost:8000/health | jq
```

---

🎉 **Congratulations!** You now have a fully functional Meeting Intelligence Agent with LangChain integration and calendar attachment capabilities!

For advanced configuration and production deployment, see the [complete documentation](readme.md).
