#!/usr/bin/env python3
"""
Process Existing Meeting Files
This script finds and processes existing meeting recordings and transcripts from Google Drive.
"""

import asyncio
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add src to path
sys.path.append('./src')

from agents.langchain_meeting_agent import get_langchain_agent

load_dotenv()

async def process_existing_meetings():
    """Process existing meeting files from Google Drive."""
    
    print("🎯 Processing Existing Meeting Files...")
    print("=" * 50)
    
    # Get the LangChain agent
    agent = get_langchain_agent()
    
    # Try to find and process existing meeting files
    tasks = [
        "Find all meeting recordings and transcripts in Google Drive",
        "Look for files with 'meeting', 'transcript', or 'notes' in the name",
        "Process the most recent meeting transcript and create a summary",
        "Generate both JSON and HTML summaries",
        "Send email notifications to meeting attendees if possible"
    ]
    
    print("📋 Tasks to execute:")
    for i, task in enumerate(tasks, 1):
        print(f"   {i}. {task}")
    
    print("\n🚀 Starting processing...")
    
    # Execute each task
    for i, task in enumerate(tasks, 1):
        print(f"\n📌 Step {i}: {task}")
        try:
            result = await agent.chat_with_agent(task)
            print(f"✅ Result: {result}")
        except Exception as e:
            print(f"❌ Error: {e}")
            continue
    
    print("\n🎉 Processing completed!")

async def interactive_meeting_processor():
    """Interactive meeting processor for manual control."""
    
    print("🤖 Interactive Meeting Processor")
    print("=" * 40)
    
    agent = get_langchain_agent()
    
    # Suggested commands
    commands = [
        "Find meeting files in Google Drive from today",
        "Find meeting files with 'product-testing' in the name",
        "Find meeting files with 'internship' in the name", 
        "Process the latest meeting transcript",
        "Summarize the most recent meeting recording",
        "Send email summary to meeting attendees"
    ]
    
    print("💡 Suggested commands:")
    for i, cmd in enumerate(commands, 1):
        print(f"   {i}. {cmd}")
    
    print("\n🎮 Interactive Mode (type 'quit' to exit):")
    
    while True:
        try:
            user_input = input("\n🤖 Enter command (or number): ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            # Check if user entered a number
            if user_input.isdigit():
                cmd_num = int(user_input)
                if 1 <= cmd_num <= len(commands):
                    command = commands[cmd_num - 1]
                    print(f"🎯 Executing: {command}")
                else:
                    print("❌ Invalid command number")
                    continue
            else:
                command = user_input
            
            # Execute the command
            print(f"⏳ Processing: {command}")
            result = await agent.chat_with_agent(command)
            print(f"✅ Result:\n{result}")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

async def main():
    """Main function."""
    
    print("🧠 Meeting Intelligence Agent - Existing Files Processor")
    print("=" * 60)
    
    if len(sys.argv) > 1 and sys.argv[1] == '--interactive':
        await interactive_meeting_processor()
    else:
        await process_existing_meetings()

if __name__ == "__main__":
    asyncio.run(main())
