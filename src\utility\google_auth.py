"""Google authentication utilities for Calendar and Drive APIs."""

import os
import logging
from typing import Optional
from google.auth.transport.requests import Request
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

logger = logging.getLogger(__name__)


class GoogleAuthenticator:
    """Handles Google API authentication for Calendar and Drive services."""
    
    # Required scopes for Calendar and Drive access
    SCOPES = [
        'https://www.googleapis.com/auth/calendar.readonly',
        'https://www.googleapis.com/auth/drive.readonly',
        'https://www.googleapis.com/auth/drive.file'
    ]
    
    def __init__(self, credentials_path: Optional[str] = None):
        """
        Initialize the authenticator.
        
        Args:
            credentials_path: Path to Google service account credentials JSON file
        """
        self.credentials_path = credentials_path or os.getenv('GOOGLE_APPLICATION_CREDENTIALS', './keys/google-service-account.json')
        self.credentials = None
        
        if not self.credentials_path:
            logger.warning("No Google credentials path provided")
        else:
            self._load_credentials()
    
    def _load_credentials(self) -> bool:
        """
        Load Google service account credentials.
        
        Returns:
            True if credentials loaded successfully, False otherwise
        """
        try:
            if not os.path.exists(self.credentials_path):
                logger.error(f"Credentials file not found: {self.credentials_path}")
                return False
            
            self.credentials = Credentials.from_service_account_file(
                self.credentials_path,
                scopes=self.SCOPES
            )
            
            logger.info("Google credentials loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load Google credentials: {e}")
            return False
    
    def get_calendar_service(self):
        """
        Get authenticated Google Calendar service.
        
        Returns:
            Google Calendar service instance or None if authentication failed
        """
        if not self.credentials:
            logger.error("No valid credentials available for Calendar service")
            return None
        
        try:
            service = build('calendar', 'v3', credentials=self.credentials)
            logger.info("Calendar service created successfully")
            return service
            
        except Exception as e:
            logger.error(f"Failed to create Calendar service: {e}")
            return None
    
    def get_drive_service(self):
        """
        Get authenticated Google Drive service.
        
        Returns:
            Google Drive service instance or None if authentication failed
        """
        if not self.credentials:
            logger.error("No valid credentials available for Drive service")
            return None
        
        try:
            service = build('drive', 'v3', credentials=self.credentials)
            logger.info("Drive service created successfully")
            return service
            
        except Exception as e:
            logger.error(f"Failed to create Drive service: {e}")
            return None
    
    def test_authentication(self) -> bool:
        """
        Test Google API authentication.
        
        Returns:
            True if authentication is working, False otherwise
        """
        try:
            # Test Calendar API
            calendar_service = self.get_calendar_service()
            if calendar_service:
                calendar_service.calendarList().list().execute()
                logger.info("Calendar API authentication test passed")
            else:
                logger.error("Calendar API authentication test failed")
                return False
            
            # Test Drive API
            drive_service = self.get_drive_service()
            if drive_service:
                drive_service.about().get(fields="user").execute()
                logger.info("Drive API authentication test passed")
            else:
                logger.error("Drive API authentication test failed")
                return False
            
            return True
            
        except HttpError as e:
            logger.error(f"Google API authentication test failed: {e}")
            return False
        except Exception as e:
            logger.error(f"Authentication test error: {e}")
            return False
    
    def refresh_credentials(self) -> bool:
        """
        Refresh Google API credentials.
        
        Returns:
            True if refresh successful, False otherwise
        """
        try:
            if self.credentials and self.credentials.expired:
                self.credentials.refresh(Request())
                logger.info("Credentials refreshed successfully")
                return True
            else:
                logger.info("Credentials are still valid")
                return True
                
        except Exception as e:
            logger.error(f"Failed to refresh credentials: {e}")
            return False
