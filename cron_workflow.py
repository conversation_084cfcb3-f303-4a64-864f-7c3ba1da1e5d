#!/usr/bin/env python3
"""
Meeting Intelligence Agent - Cron Workflow Launcher
Simple launcher that calls the actual cron workflow script in src/utility
"""

import os
import sys
from pathlib import Path

def main():
    """Launch the actual cron workflow script from src/utility"""
    # Get the project root directory
    project_root = Path(__file__).parent.absolute()
    
    # Path to the actual cron workflow script
    workflow_script = project_root / 'src' / 'utility' / 'cron_workflow.py'
    
    if not workflow_script.exists():
        print(f"Error: Cron workflow script not found at {workflow_script}")
        sys.exit(1)
    
    # Change to project root and run the workflow script
    os.chdir(project_root)
    
    # Forward all command line arguments to the actual script
    import subprocess
    try:
        result = subprocess.run([sys.executable, str(workflow_script)] + sys.argv[1:])
        sys.exit(result.returncode)
    except Exception as e:
        print(f"Error running cron workflow: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 